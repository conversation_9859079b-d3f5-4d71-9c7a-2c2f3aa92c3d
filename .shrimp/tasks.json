{"tasks": [{"id": "d2ced1af-86dd-459f-aac7-200b09707367", "name": "Snippets目录李继刚文件发现与清单建立", "description": "递归扫描Snippets根目录及其所有子目录，识别文件名包含prompt、李继刚或lijigang的所有文件，建立完整的待处理文档清单。重点检查readwise/Full Document Contents/Tweets和readwise/Tweets目录下的李继刚相关文件，记录文件路径、类型和初步内容特征", "notes": "这是整个项目的基础任务，必须确保不遗漏任何李继刚相关文件。特别关注readwise目录下的推文内容，这些可能包含完整的提示词", "status": "completed", "dependencies": [], "createdAt": "2025-08-04T04:00:06.710Z", "updatedAt": "2025-08-04T04:01:32.797Z", "relatedFiles": [{"path": "Snippets/", "type": "REFERENCE", "description": "主要扫描目录"}, {"path": "Snippets/readwise/", "type": "REFERENCE", "description": "重点扫描的子目录"}, {"path": "Snippets/Prompt三则(6).md", "type": "TO_MODIFY", "description": "已知的多提示词文档"}], "implementationGuide": "使用view工具递归扫描Snippets目录结构，按以下步骤执行：\n1. 扫描Snippets根目录，识别直接包含关键词的文件\n2. 深入readwise子目录，重点检查Tweets和Full Document Contents\n3. 对每个发现的文件记录：文件路径、文件大小、修改时间\n4. 初步判断文件类型：单一提示词、多提示词、非提示词内容\n5. 生成结构化清单，按优先级排序（提示词文档优先）", "verificationCriteria": "完成后应生成包含所有李继刚相关文件的结构化清单，清单应包含文件路径、类型判断、优先级排序，确保无遗漏", "summary": "成功发现并建立了Snippets目录下所有李继刚相关文件的完整清单，总计约65个文件，包括1个多提示词文档、约50个单一提示词文档和约14个非提示词内容，按优先级分类完成，确保无遗漏", "completedAt": "2025-08-04T04:01:32.792Z"}, {"id": "91b1c0a8-285f-4b7f-9713-76fc2fe450ec", "name": "提示词文档内容分析与分类", "description": "对发现的李继刚相关文件进行深度内容分析，识别真正的提示词文档，区分单一提示词与多提示词文档，提取主题关键词用于后续命名和分类。重点分析文档结构特征，如Role定义、===分隔符、系统指令等", "status": "completed", "dependencies": [{"taskId": "d2ced1af-86dd-459f-aac7-200b09707367"}], "createdAt": "2025-08-04T04:00:06.710Z", "updatedAt": "2025-08-04T04:02:41.615Z", "implementationGuide": "请根据任务描述执行", "summary": "已完成对所有相关文件的深度内容分析。成功识别了真正的提示词文档，并将其区分为“单一提示词”和“多提示词”两类。同时，为每个提示词文档提取了核心主题关键词，并对文档的结构特征（如角色定义、分隔符等）进行了标注。这为后续的标准化命名和分类工作提供了清晰的依据。", "completedAt": "2025-08-04T04:02:41.614Z"}, {"id": "6c4bec88-0dba-42e2-af83-246ac4a1041a", "name": "Documents目录现有文档检查与版本比较", "description": "检查Documents目录下现有的李继刚文档，与Snippets中发现的文件进行内容比较，识别重复文档和版本差异。基于内容完整性、YAML规范性、功能描述详细程度等标准，确定保留哪个版本", "status": "pending", "dependencies": [{"taskId": "91b1c0a8-285f-4b7f-9713-76fc2fe450ec"}], "createdAt": "2025-08-04T04:00:06.710Z", "updatedAt": "2025-08-04T04:00:06.710Z", "implementationGuide": "请根据任务描述执行"}, {"id": "5f0dce12-4cda-46c3-a241-039636929f95", "name": "多提示词文档拆分处理", "description": "对识别出的多提示词文档进行拆分处理，将每个独立的提示词提取为单独文件。按照李继刚文档标准生成YAML头部，确保每个拆分后的文件都符合规范要求", "status": "pending", "dependencies": [{"taskId": "91b1c0a8-285f-4b7f-9713-76fc2fe450ec"}], "createdAt": "2025-08-04T04:00:06.710Z", "updatedAt": "2025-08-04T04:00:06.710Z", "implementationGuide": "请根据任务描述执行"}, {"id": "58aa8c66-06f4-41dc-a74d-f4851c1e3aab", "name": "单一提示词文档标准化处理", "description": "对识别出的单一提示词文档进行标准化处理，应用李继刚文档YAML规范，确保所有字段完整且格式正确。重点处理readwise目录下的推文提示词文档", "status": "pending", "dependencies": [{"taskId": "91b1c0a8-285f-4b7f-9713-76fc2fe450ec"}], "createdAt": "2025-08-04T04:00:06.710Z", "updatedAt": "2025-08-04T04:00:06.710Z", "implementationGuide": "请根据任务描述执行"}, {"id": "fce2e74b-5151-4984-a133-25f4c6ec70a3", "name": "标准化文档迁移到Documents目录", "description": "将处理完成的标准化李继刚文档迁移到Documents目录，执行原子化操作确保数据零丢失。先创建新文件并验证完整性，再删除源文件", "status": "pending", "dependencies": [{"taskId": "5f0dce12-4cda-46c3-a241-039636929f95"}, {"taskId": "58aa8c66-06f4-41dc-a74d-f4851c1e3aab"}, {"taskId": "6c4bec88-0dba-42e2-af83-246ac4a1041a"}], "createdAt": "2025-08-04T04:00:06.710Z", "updatedAt": "2025-08-04T04:00:06.710Z", "implementationGuide": "请根据任务描述执行"}, {"id": "a7b03edc-676b-4883-94c4-463563d75305", "name": "重复文档清理与版本整合", "description": "根据版本比较结果，清理重复的李继刚文档，保留最优版本。确保Documents目录下没有重复内容，所有保留的文档都是最完整和最规范的版本", "status": "pending", "dependencies": [{"taskId": "fce2e74b-5151-4984-a133-25f4c6ec70a3"}], "createdAt": "2025-08-04T04:00:06.710Z", "updatedAt": "2025-08-04T04:00:06.710Z", "implementationGuide": "请根据任务描述执行"}, {"id": "1d6f4fb2-5100-4b33-b0bd-9eb22253549a", "name": "整理结果验证与质量检查", "description": "对整个李继刚文档整理项目进行全面的质量检查和验证，确保所有文档都符合标准，没有遗漏或错误。生成整理报告总结项目成果", "status": "pending", "dependencies": [{"taskId": "a7b03edc-676b-4883-94c4-463563d75305"}], "createdAt": "2025-08-04T04:00:06.710Z", "updatedAt": "2025-08-04T04:00:06.710Z", "implementationGuide": "请根据任务描述执行"}]}